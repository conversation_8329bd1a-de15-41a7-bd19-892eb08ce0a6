#!/usr/bin/env node

/**
 * 简单的测试脚本，验证硬编码配置是否工作
 */

console.log('🔧 测试硬编码配置...\n');

// 模拟硬编码配置
const HARDCODED_MODEL_CONFIG = {
  apiKey: 'your-api-key-here',
  baseUrl: 'https://api.openai.com/v1',
  model: 'gpt-3.5-turbo',
  timeout: 120000,
  maxRetries: 3,
  userAgent: 'Qwen-Code/1.0.0',
  samplingParams: {
    temperature: 0.7,
    max_tokens: 4096,
    top_p: 1.0,
  }
};

// 显示当前配置
console.log('📋 当前配置:');
console.log('  API Key:', HARDCODED_MODEL_CONFIG.apiKey);
console.log('  Base URL:', HARDCODED_MODEL_CONFIG.baseUrl);
console.log('  Model:', HARDCODED_MODEL_CONFIG.model);
console.log('  Timeout:', HARDCODED_MODEL_CONFIG.timeout);
console.log('  Max Retries:', HARDCODED_MODEL_CONFIG.maxRetries);
console.log('');

// 验证配置
function validateHardcodedConfig() {
  if (!HARDCODED_MODEL_CONFIG.apiKey || HARDCODED_MODEL_CONFIG.apiKey === 'your-api-key-here') {
    return 'Please update HARDCODED_MODEL_CONFIG.apiKey in packages/core/src/config/hardcoded-config.ts';
  }

  if (!HARDCODED_MODEL_CONFIG.baseUrl) {
    return 'Please update HARDCODED_MODEL_CONFIG.baseUrl in packages/core/src/config/hardcoded-config.ts';
  }

  if (!HARDCODED_MODEL_CONFIG.model) {
    return 'Please update HARDCODED_MODEL_CONFIG.model in packages/core/src/config/hardcoded-config.ts';
  }

  return null;
}

const validationError = validateHardcodedConfig();
if (validationError) {
  console.error('❌ 配置验证失败:');
  console.error('  ', validationError);
  console.log('');
  console.log('💡 请编辑文件: packages/core/src/config/hardcoded-config.ts');
  console.log('   更新您的API密钥、基础URL和模型名称');
  process.exit(1);
} else {
  console.log('✅ 配置验证成功!');
  console.log('');
  console.log('🚀 您可以继续使用 Qwen Code');
  console.log('   运行: npm start');
}
