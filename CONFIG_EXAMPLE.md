# Qwen Code 硬编码配置指南

本项目已经简化为使用硬编码配置，移除了所有复杂的认证机制。

## 🔧 配置步骤

### 1. 修改硬编码配置文件

编辑文件：`packages/core/src/config/hardcoded-config.ts`

```typescript
export const HARDCODED_MODEL_CONFIG = {
  // API 配置 - 请替换为您的实际配置
  apiKey: 'your-api-key-here',           // 替换为实际的API密钥
  baseUrl: 'https://api.openai.com/v1',  // 替换为实际的API端点
  model: 'gpt-3.5-turbo',                // 替换为实际的模型名称
  
  // 可选配置
  timeout: 120000,     // 120秒超时
  maxRetries: 3,       // 最大重试次数
  
  // 用户代理
  userAgent: 'Qwen-Code/1.0.0',
  
  // 默认采样参数
  samplingParams: {
    temperature: 0.7,
    max_tokens: 4096,
    top_p: 1.0,
  }
};
```

### 2. 支持的API提供商

#### OpenAI 兼容 API
```typescript
{
  apiKey: 'sk-your-openai-key',
  baseUrl: 'https://api.openai.com/v1',
  model: 'gpt-4'
}
```

#### 阿里云百炼 (中国大陆)
```typescript
{
  apiKey: 'your-dashscope-key',
  baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
  model: 'qwen-coder-plus'
}
```

#### 阿里云 ModelStudio (国际)
```typescript
{
  apiKey: 'your-modelstudio-key', 
  baseUrl: 'https://modelstudio.aliyun.com/api/v1',
  model: 'qwen-coder-plus'
}
```

#### 其他兼容提供商
- **OpenRouter**: `https://openrouter.ai/api/v1`
- **DeepSeek**: `https://api.deepseek.com/v1`
- **Moonshot**: `https://api.moonshot.cn/v1`
- **智谱AI**: `https://open.bigmodel.cn/api/paas/v4`

### 3. 构建和运行

```bash
# 安装依赖
npm install

# 构建项目
npm run build

# 运行程序
npm start
```

## ⚠️ 重要提示

1. **安全性**: 请不要将包含真实API密钥的配置文件提交到版本控制系统
2. **配置验证**: 程序启动时会自动验证配置的有效性
3. **错误处理**: 如果配置无效，程序会显示具体的错误信息

## 🔍 故障排除

### 配置错误
如果看到以下错误：
```
Please update HARDCODED_MODEL_CONFIG.apiKey in packages/core/src/config/hardcoded-config.ts
```

请检查：
1. API密钥是否正确设置
2. 基础URL是否正确
3. 模型名称是否有效

### 网络连接错误
如果遇到网络连接问题：
1. 检查网络连接
2. 验证API端点是否可访问
3. 确认API密钥权限

## 📝 更多配置选项

可以在 `samplingParams` 中调整模型行为：

- `temperature`: 控制输出随机性 (0.0-2.0)
- `max_tokens`: 最大输出长度
- `top_p`: 核采样参数 (0.0-1.0)
- `frequency_penalty`: 频率惩罚 (-2.0-2.0)
- `presence_penalty`: 存在惩罚 (-2.0-2.0)
