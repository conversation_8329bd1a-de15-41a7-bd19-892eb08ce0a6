/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import {
  CountTokensResponse,
  GenerateContentResponse,
  GenerateContentParameters,
  CountTokensParameters,
  EmbedContentResponse,
  EmbedContentParameters,
} from '@google/genai';
import { Config } from '../config/config.js';
import { HARDCODED_MODEL_CONFIG } from '../config/hardcoded-config.js';

import { UserTierId } from '../code_assist/types.js';

/**
 * Interface abstracting the core functionalities for generating content and counting tokens.
 */
export interface ContentGenerator {
  generateContent(
    request: GenerateContentParameters,
    userPromptId: string,
  ): Promise<GenerateContentResponse>;

  generateContentStream(
    request: GenerateContentParameters,
    userPromptId: string,
  ): Promise<AsyncGenerator<GenerateContentResponse>>;

  countTokens(request: CountTokensParameters): Promise<CountTokensResponse>;

  embedContent(request: EmbedContentParameters): Promise<EmbedContentResponse>;

  userTier?: UserTierId;
}

export enum AuthType {
  HARDCODED = 'hardcoded',
}

export type ContentGeneratorConfig = {
  model: string;
  apiKey?: string;
  baseUrl?: string;
  vertexai?: boolean;
  authType?: AuthType | undefined;
  enableOpenAILogging?: boolean;
  // Timeout configuration in milliseconds
  timeout?: number;
  // Maximum retries for failed requests
  maxRetries?: number;
  // Disable cache control for DashScope providers
  disableCacheControl?: boolean;
  samplingParams?: {
    top_p?: number;
    top_k?: number;
    repetition_penalty?: number;
    presence_penalty?: number;
    frequency_penalty?: number;
    temperature?: number;
    max_tokens?: number;
  };
  proxy?: string | undefined;
  userAgent?: string;
};

export function createContentGeneratorConfig(
  config: Config,
  authType: AuthType | undefined,
): ContentGeneratorConfig {
  // 使用硬编码配置，不再依赖环境变量和认证
  const hardcodedConfig = HARDCODED_MODEL_CONFIG;

  const contentGeneratorConfig: ContentGeneratorConfig = {
    model: hardcodedConfig.model,
    authType: AuthType.HARDCODED,
    apiKey: hardcodedConfig.apiKey,
    baseUrl: hardcodedConfig.baseUrl,
    timeout: hardcodedConfig.timeout,
    maxRetries: hardcodedConfig.maxRetries,
    enableOpenAILogging: config.getEnableOpenAILogging(),
    samplingParams: hardcodedConfig.samplingParams,
    vertexai: false, // 不使用Vertex AI
    proxy: config?.getProxy(),
    disableCacheControl: config.getContentGeneratorDisableCacheControl(),
  };

  return contentGeneratorConfig;
}

export async function createContentGenerator(
  config: ContentGeneratorConfig,
  gcConfig: Config,
  sessionId?: string,
): Promise<ContentGenerator> {
  // 简化版本，只使用OpenAI兼容的生成器，移除所有认证逻辑
  if (!config.apiKey) {
    throw new Error('API key is required in hardcoded configuration');
  }

  // 动态导入OpenAIContentGenerator
  const { OpenAIContentGenerator } = await import('./openaiContentGenerator.js');

  return new OpenAIContentGenerator(config, gcConfig);
}
