/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * 硬编码配置文件 - 移除所有认证，直接配置模型参数
 */

// 硬编码的模型配置
export const HARDCODED_MODEL_CONFIG = {
  // API 配置
  apiKey: 'your-api-key-here', // 请替换为实际的API密钥
  baseUrl: 'https://api.openai.com/v1', // 请替换为实际的API端点
  model: 'gpt-3.5-turbo', // 请替换为实际的模型名称
  
  // 可选配置
  timeout: 120000, // 120秒超时
  maxRetries: 3,
  
  // 用户代理
  userAgent: 'Qwen-Code/1.0.0',
  
  // 默认采样参数
  samplingParams: {
    temperature: 0.7,
    max_tokens: 4096,
    top_p: 1.0,
  }
};

// 简化的认证类型 - 只保留必要的
export enum SimpleAuthType {
  HARDCODED = 'hardcoded',
}

// 验证配置是否有效
export function validateHardcodedConfig(): string | null {
  if (!HARDCODED_MODEL_CONFIG.apiKey || HARDCODED_MODEL_CONFIG.apiKey === 'your-api-key-here') {
    return 'Please update HARDCODED_MODEL_CONFIG.apiKey in packages/core/src/config/hardcoded-config.ts';
  }
  
  if (!HARDCODED_MODEL_CONFIG.baseUrl) {
    return 'Please update HARDCODED_MODEL_CONFIG.baseUrl in packages/core/src/config/hardcoded-config.ts';
  }
  
  if (!HARDCODED_MODEL_CONFIG.model) {
    return 'Please update HARDCODED_MODEL_CONFIG.model in packages/core/src/config/hardcoded-config.ts';
  }
  
  return null;
}
