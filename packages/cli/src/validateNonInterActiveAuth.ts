/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { AuthType, Config, validateHardcodedConfig } from '@qwen-code/qwen-code-core';

export async function validateNonInteractiveAuth(
  configuredAuthType: AuthType | undefined,
  useExternalAuth: boolean | undefined,
  nonInteractiveConfig: Config,
) {
  // 简化非交互式认证验证，只使用硬编码配置
  const effectiveAuthType = AuthType.HARDCODED;

  // 验证硬编码配置
  const configError = validateHardcodedConfig();
  if (configError) {
    console.error(configError);
    process.exit(1);
  }

  // 刷新认证配置
  await nonInteractiveConfig.refreshAuth(effectiveAuthType);
  return nonInteractiveConfig;
}
