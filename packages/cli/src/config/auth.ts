/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { AuthType } from '@qwen-code/qwen-code-core';
import { validateHardcodedConfig } from '@qwen-code/qwen-code-core';

export const validateAuthMethod = (authMethod: string): string | null => {
  // 简化认证验证，只检查硬编码配置
  if (authMethod === AuthType.HARDCODED) {
    return validateHardcodedConfig();
  }

  return 'Invalid auth method selected. Only hardcoded configuration is supported.';
};

export const setOpenAIApiKey = (apiKey: string): void => {
  process.env['OPENAI_API_KEY'] = apiKey;
};

export const setOpenAIBaseUrl = (baseUrl: string): void => {
  process.env['OPENAI_BASE_URL'] = baseUrl;
};

export const setOpenAIModel = (model: string): void => {
  process.env['OPENAI_MODEL'] = model;
};
